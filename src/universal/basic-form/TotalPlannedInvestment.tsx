import { useQuery } from '@tanstack/react-query'
import { Card, message } from 'antd'
import dayjs from 'dayjs'
import { CalendarClockIcon } from 'lucide-react'
import numeral from 'numeral'

import { useAuth } from '@/contexts/auth'
import { request, type APIResponse } from '@/lib/request'

export const TotalPlannedInvestment = ({
  plannedUrl,
}: {
  plannedUrl: string
}) => {
  const { user } = useAuth()

  const getTotalPlannedInvestment = useQuery({
    queryKey: [plannedUrl, dayjs().year().toString()],
    queryFn: async ({ queryKey: [url, investment_year] }) => {
      const response = await request<APIResponse<{ total: number }>>(url, {
        query: { investment_year },
      })
      if (response.code !== 200001) {
        message.error(response.message)
        return null
      }
      return response.data.total
    },
    staleTime: 0,
  })

  return (
    <Card loading={getTotalPlannedInvestment.isFetching}>
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">{user?.company}</h2>
        <div className="flex items-center gap-2">
          <CalendarClockIcon className="size-4" />
          <span className="text-sm text-[#666]">本年度项目计划总投资：</span>
          <span className="text-xl font-semibold">
            {numeral(getTotalPlannedInvestment.data).format('0,0.00')}万元
          </span>
        </div>
      </div>
    </Card>
  )
}
