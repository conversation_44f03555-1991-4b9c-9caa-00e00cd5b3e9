import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate, useParams } from '@tanstack/react-router'
import {
  Button,
  Cascader,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Select,
  Spin,
  Upload,
  type UploadFile,
} from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'

import { useApp } from '@/contexts/app'
import { useAuth } from '@/contexts/auth.tsx'
import { type APIResponse, request } from '@/lib/request.ts'
import { getIndustryNewTypePath } from '@/universal/basic-form'
import {
  INDUSTRY_NEW_TYPE,
  INDUSTRY_TYPE,
  OVERSEA_PROJECT_AREA,
  PROJECT_AREA,
  PROJECT_CATEGORY,
} from '@/universal/basic-form/constants.ts'
import type {
  PostEvaluationDTO,
  PostEvaluationForm,
} from '@/universal/basic-form/types.ts'

export function PostEvaluationForm({
  isUpdate,
}: Readonly<{ isUpdate?: boolean }>) {
  const { user } = useAuth()

  const navigate = useNavigate()
  const { id } = useParams({ strict: false })

  const { date } = useApp()

  const [form] = Form.useForm()
  const [messageApi, contextHolder] = message.useMessage()

  const [fileList, setFileList] = useState<UploadFile[]>([])

  const submitFormFile = useMutation({
    mutationFn: async (project_id: string | undefined) => {
      const files = fileList?.map((file) => {
        if (file.response) return file.response
        return {
          id: file.uid,
          name: file.name,
          url: file.url,
        }
      })

      try {
        const res = await request('/post-evaluation/set-upload-files', {
          method: 'POST',
          body: { project_id, files },
        })
        if (res.code === 200001) return message.success('操作成功')

        message.error(res?.message)
      } catch (error) {
        message.error('操作失败: ' + error)
      } finally {
        await navigate({ to: '/basic-report/post-evaluation' })
      }
    },
  })

  const getFormData = useQuery({
    queryKey: ['id', id],
    queryFn: async ({ queryKey: [, id] }) => {
      const res = await request<APIResponse<PostEvaluationDTO>>(
        `/post-evaluation/detail-by-approval-node-id?approval_node_id=${id}`,
      )
      if (res.code === 200001) {
        setFileList(
          res?.data?.files?.map(
            (item: Record<string, string>, index: number) => ({
              ...item,
              uid: item.id || item.uid || `file-${index}`,
              name: item.name || item.filename || `file-${index}`,
              status: 'done' as const,
              url: item.url,
            }),
          ),
        )
        const {
          complete_time_expect,
          industry_new_type,
          investment_year,
          project_category,
          start_time,
          ...data
        } = res.data

        const formData = {
          ...data,
          complete_time_expect: dayjs(complete_time_expect),
          industry_new_type: getIndustryNewTypePath(industry_new_type),
          investment_year: dayjs(investment_year),
          project_category: project_category?.split(','),
          start_time: dayjs(start_time),
        }

        form.setFieldsValue(formData)
        return formData
      }

      message.error(res.message)
    },
    enabled: !!id,
    refetchOnWindowFocus: false,
  })

  const submitForm = useMutation({
    mutationFn: async (values: PostEvaluationForm) => {
      const {
        complete_time_expect,
        industry_new_type,
        investment_year,
        project_category,
        start_time,
        ...data
      } = values

      const bodyData = {
        ...data,
        complete_time_expect: dayjs(complete_time_expect).format('YYYY-MM-DD'),
        industry_new_type: industry_new_type?.[industry_new_type.length - 1],
        investment_year: dayjs(investment_year).format('YYYY'),
        project_category: Array.isArray(project_category)
          ? project_category.join(',')
          : project_category,
        start_time: dayjs(start_time).format('YYYY-MM-DD'),
      }

      if (isUpdate) {
        const res = await request('/post-evaluation/modify', {
          method: 'PUT',
          body: { approval_node_id: id, ...bodyData },
        })
        if (res.code === 200001) {
          return getFormData.data?.project_id
        }
        message.error(res?.message)
        return
      }

      const res = await request<
        APIResponse<{
          project_id: string
          project_version_id: string
          approval_id: string
          approval_node_id: string
        }>
      >('/post-evaluation/create', {
        method: 'POST',
        body: bodyData,
      })
      if (res.code === 200001) {
        return res.data.project_id
      }
      message.error(res?.message)
    },

    onError: (err) => message.error(JSON.stringify(err)),
  })

  const watchRegion = Form.useWatch('region', form)

  return (
    <Spin
      spinning={
        submitForm.isPending ||
        getFormData.isFetching ||
        submitFormFile.isPending
      }
    >
      <Form
        form={form}
        labelCol={{ style: { width: '160px' } }}
        labelAlign="left"
        scrollToFirstError={{ block: 'center', behavior: 'smooth' }}
        onFinish={async (values) => {
          const project_id = await submitForm.mutateAsync(values)
          submitFormFile.mutateAsync(project_id)
        }}
      >
        <div className="flex flex-col gap-6">
          <h4 className="text-sm font-semibold text-[#266EFF]">项目基础信息</h4>
          <div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="填报年份"
                name="investment_year"
                rules={[{ required: true, message: '请输入填报年份' }]}
                initialValue={date}
              >
                <DatePicker
                  picker="year"
                  className="w-full"
                  placeholder="请选择填报年份"
                  format="YYYY"
                />
              </Form.Item>
              <Form.Item
                label="编制单位"
                name="company_id"
                rules={[{ required: true, message: '请选择编制单位' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择编制单位"
                  options={[{ label: user?.company, value: user?.company_id }]}
                  onChange={(_, option) => {
                    if (Array.isArray(option)) {
                      form.setFieldValue('company_name', option[0]?.label)
                    } else {
                      form.setFieldValue('company_name', option?.label)
                    }
                  }}
                />
              </Form.Item>
              <Form.Item name="company_name" noStyle></Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="国资/股权"
                name="project_style"
                rules={[{ required: true, message: '请选择国资/股权类型' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择国资/股权类型"
                  options={[
                    { label: '固定资产投资', value: 1 },
                    { label: '股权投资', value: 2 },
                  ]}
                />
              </Form.Item>
              <Form.Item
                label="项目名称"
                name="project_name"
                rules={[{ required: true, message: '请输入项目名称' }]}
              >
                <Input className="w-full" placeholder="请输入项目名称" />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="境内/境外"
                name="region"
                rules={[{ required: true, message: '请选择境内/境外' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择境内/境外"
                  options={[
                    { label: '境内', value: 1 },
                    { label: '境外', value: 2 },
                  ]}
                />
              </Form.Item>
              <Form.Item
                label={
                  <span className="h-12">
                    省、自治区、直辖
                    <br />
                    市/国家（地区）
                  </span>
                }
                name="project_area"
                rules={[{ required: true, message: '请选择区域' }]}
              >
                {watchRegion === 1 ? (
                  <Select
                    placeholder="请选择省/自治区/直辖市或国家（地区）"
                    showSearch
                    options={PROJECT_AREA.map((item) => ({
                      label: item,
                      value: item,
                    }))}
                  />
                ) : (
                  <Select
                    className="w-full"
                    placeholder="请选择省/自治区/直辖市或国家（地区）"
                    popupRender={(menu) => (
                      <>
                        {menu}
                        <Divider style={{ margin: '8px 0' }} />
                        <Input
                          placeholder="输入新地点"
                          onChange={(e) =>
                            form.setFieldValue('project_area', e.target.value)
                          }
                          onKeyDown={(e) => e.stopPropagation()}
                        />
                      </>
                    )}
                    options={OVERSEA_PROJECT_AREA.map((item) => ({
                      label: item,
                      value: item,
                    }))}
                  />
                )}
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="主业/非主业"
                name="is_major"
                rules={[{ required: true, message: '请选择主业/非主业' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择主业/非主业"
                  options={[
                    { label: '主业', value: 1 },
                    { label: '非主业', value: 2 },
                  ]}
                />
              </Form.Item>
              <Form.Item
                label="项目分类"
                name="project_category"
                rules={[{ required: true, message: '请选择项目分类' }]}
              >
                <Select
                  allowClear
                  className="w-full"
                  labelRender={({ value }) => value}
                  mode="multiple"
                  onSearch={(value) =>
                    PROJECT_CATEGORY.filter((item) =>
                      item.label.includes(value),
                    )
                  }
                  optionFilterProp="label"
                  options={PROJECT_CATEGORY}
                  placeholder="请选择项目分类"
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="所属行业"
                name="industry_type"
                rules={[{ required: true, message: '请选择所属行业' }]}
              >
                <Select
                  allowClear
                  className="w-full"
                  labelRender={({ label, value }) =>
                    `${label as string}${value}`
                  }
                  onSearch={(value) =>
                    INDUSTRY_TYPE.filter((item) => item.label.includes(value))
                  }
                  optionFilterProp="label"
                  options={INDUSTRY_TYPE}
                  placeholder="请选择所属行业"
                  showSearch
                />
              </Form.Item>
              <Form.Item label="所属战新产业" name="industry_new_type">
                <Cascader
                  className="w-full"
                  placeholder="请选择所属战新产业"
                  options={INDUSTRY_NEW_TYPE}
                  showSearch
                  allowClear
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="项目总投资"
                name="project_total_investment"
                rules={[{ required: true, message: '请输入项目总投资' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="请输入项目总投资"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
              <Form.Item
                label="项目开始时间"
                name="start_time"
                rules={[{ required: true, message: '请选择项目开始时间' }]}
              >
                <DatePicker
                  className="w-full"
                  format="YYYY-MM-DD"
                  placeholder="请选择项目开始时间"
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label={
                  <span className="h-12">
                    项目完成或预计
                    <br />
                    完成时间
                  </span>
                }
                name="complete_time_expect"
                rules={[
                  { required: true, message: '请选择完成或预计完成时间' },
                ]}
              >
                <DatePicker
                  className="w-full"
                  format="YYYY-MM-DD"
                  placeholder="请选择完成或预计完成时间"
                />
              </Form.Item>
              <Form.Item
                label="组织形式"
                name="organization_type"
                rules={[{ required: true, message: '请选择组织形式' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择组织形式"
                  options={[
                    { label: '集团组织', value: 1 },
                    { label: '子企业组织', value: 2 },
                  ]}
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="评价方式"
                name="eval_type"
                rules={[{ required: true, message: '请选择评价方式' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择评价方式"
                  options={[
                    { label: '企业自评', value: 1 },
                    { label: '第三方评价', value: 2 },
                  ]}
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1">
              <Form.Item label="备注" name="remarks">
                <Input.TextArea
                  className="w-full"
                  placeholder="请输入备注"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
          </div>

          <h4 className="text-sm font-semibold text-[#266EFF]">项目完成情况</h4>
          <div>
            <div className="grid grid-cols-1">
              <Form.Item
                label="工作成效"
                name="performance"
                rules={[{ required: true, message: '请输入工作成效' }]}
              >
                <Input.TextArea
                  className="w-full"
                  placeholder="请输入投资后评价工作对企业投资管理、投资风险防控等方面的成效..."
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1">
          <Form.Item label="附件材料">
            {contextHolder}
            <Upload
              accept=".doc,.docx,.ppt,.pdf,.xlsx,.zip,.tar,.png,.jpg,.jpeg,.gif,.rar"
              action="/files"
              customRequest={async ({ action, file, onSuccess, onError }) => {
                messageApi.open({
                  type: 'loading',
                  content: '文件上传中...',
                  duration: 0,
                })

                const formData = new FormData()
                formData.append('file', file)

                try {
                  const res = await request(action, {
                    method: 'POST',
                    body: formData,
                  })
                  messageApi.destroy()
                  if (res.code === 200001) {
                    onSuccess?.(res.data)
                    messageApi.success('文件上传成功')
                  } else {
                    onError?.(new Error(res.message))
                    messageApi.error('文件上传失败:' + res.message)
                  }
                } catch (error) {
                  const err =
                    error instanceof Error ? error : new Error(String(error))
                  onError?.(err)
                  messageApi.error('文件上传失败:' + err.message)
                }
              }}
              beforeUpload={(file) => {
                const isValidated = file.size / 1024 / 1024 < 50
                if (!isValidated) {
                  message.error('单个文件大小不能超过50MB')
                  return false
                }
                return true
              }}
              maxCount={10}
              multiple
              showUploadList={{
                showPreviewIcon: true,
                showDownloadIcon: true,
                showRemoveIcon: true,
              }}
              fileList={fileList}
              onChange={(info) => {
                setFileList(
                  info.fileList.map((file) => {
                    if (file.response) {
                      return { ...file, url: file.response.url }
                    }

                    return file
                  }),
                )
              }}
              onDownload={async (file) => {
                try {
                  if (!file?.url) {
                    message.error('文件下载链接不存在')
                    return
                  }
                  const response = await fetch(file.url)
                  const blob = await response.blob()
                  const url = URL.createObjectURL(blob)
                  const link = document.createElement('a')
                  link.href = url
                  link.download = file.name
                  document.body.appendChild(link)
                  link.click()
                  document.body.removeChild(link)
                  URL.revokeObjectURL(url)
                } catch (error) {
                  message.error('文件下载失败:' + error)
                }
              }}
            >
              <Button>点击上传</Button>
            </Upload>
          </Form.Item>
        </div>

        <div className="sticky bottom-0 flex justify-end gap-2 bg-white py-2">
          <Button type="primary" htmlType="submit">
            {isUpdate ? '更新数据' : '保存数据'}
          </Button>
          <Button
            onClick={() => navigate({ to: '/basic-report/post-evaluation' })}
          >
            取消
          </Button>
        </div>
      </Form>
    </Spin>
  )
}
